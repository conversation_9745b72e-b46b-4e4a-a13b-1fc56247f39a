import React from 'react';
import { cn } from '@/lib/utils';
import { ArrowLeft } from 'lucide-react';

interface BackButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children?: React.ReactNode;
  showIcon?: boolean;
  className?: string;
}

const BackButton = React.forwardRef<HTMLButtonElement, BackButtonProps>(
  ({ children = "Nazad", showIcon = true, className, ...props }, ref) => {
    return (
      <button
        ref={ref}
        className={cn(
          "inline-flex items-center gap-2 px-4 py-2 text-sm font-medium",
          "bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-800 dark:to-slate-800",
          "border border-gray-200 dark:border-gray-700",
          "text-gray-700 dark:text-gray-300",
          "hover:bg-gradient-to-r hover:from-gray-100 hover:to-slate-100 dark:hover:from-gray-700 dark:hover:to-slate-700",
          "hover:border-gray-300 dark:hover:border-gray-600",
          "rounded-lg transition-all duration-200",
          "hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          className
        )}
        {...props}
      >
        {showIcon && <ArrowLeft className="w-4 h-4" />}
        {children}
      </button>
    );
  }
);

BackButton.displayName = "BackButton";

export { BackButton };
