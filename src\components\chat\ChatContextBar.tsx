'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ExternalLink,
  Calendar,
  DollarSign,
  Target,
  Building2,
  User,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import Link from 'next/link';
import { formatDate } from '@/lib/date-utils';

interface ChatContextBarProps {
  campaignApplicationId?: string | null;
  offerId?: string | null;
}

interface CampaignData {
  id: string;
  title: string;
  budget: number;
  end_date: string | null;
  status: string;
  company_name: string;
}

interface OfferData {
  id: string;
  title: string;
  budget: number;
  deadline: string | null;
  status: string;
  influencer_name: string;
  business_name: string;
}

export function ChatContextBar({
  campaignApplicationId,
  offerId,
}: ChatContextBarProps) {
  const { user } = useAuth();
  const [campaignData, setCampaignData] = useState<CampaignData | null>(null);
  const [offerData, setOfferData] = useState<OfferData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadContextData();
  }, [campaignApplicationId, offerId]);

  const loadContextData = async () => {
    setLoading(true);
    try {
      if (campaignApplicationId) {
        await loadCampaignData();
      } else if (offerId) {
        await loadOfferData();
      }
    } catch (error) {
      console.error('Error loading context data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCampaignData = async () => {
    if (!campaignApplicationId) {
      console.log('No campaignApplicationId provided');
      return;
    }

    console.log('Loading campaign data for application ID:', campaignApplicationId);

    try {
      // First get the campaign application
      const { data: application, error: appError } = await supabase
        .from('campaign_applications')
        .select('id, status, campaign_id')
        .eq('id', campaignApplicationId)
        .single();

      if (appError) {
        console.error('Error loading campaign application:', appError);
        return;
      }

      if (!application?.campaign_id) {
        console.log('No campaign ID found in application');
        return;
      }

      // Then get the campaign with business info
      const { data: campaign, error: campaignError } = await supabase
        .from('campaigns')
        .select(`
          id,
          title,
          budget,
          end_date,
          status,
          business_id,
          businesses (
            company_name
          )
        `)
        .eq('id', application.campaign_id)
        .single();

      if (campaignError) {
        console.error('Error loading campaign data:', campaignError);
        return;
      }

      console.log('Campaign data:', campaign);

      if (campaign) {
        const campaignInfo = {
          id: campaign.id,
          title: campaign.title,
          budget: campaign.budget,
          end_date: campaign.end_date,
          status: application.status,
          company_name: campaign.businesses?.company_name || 'Nepoznato',
        };
        console.log('Setting campaign data:', campaignInfo);
        setCampaignData(campaignInfo);
      } else {
        console.log('No campaign data found');
      }
    } catch (error) {
      console.error('Error loading campaign data:', error);
    }
  };

  const loadOfferData = async () => {
    if (!offerId) return;

    try {
      // Get offer data with business info
      const { data: offer, error } = await supabase
        .from('direct_offers')
        .select(
          `
          id,
          title,
          budget,
          deadline,
          status,
          influencer_id,
          businesses (
            company_name
          )
        `
        )
        .eq('id', offerId)
        .single();

      if (error) {
        console.error('Error loading offer data:', error);
        return;
      }

      // Get influencer profile separately
      const { data: influencerProfile } = await supabase
        .from('profiles')
        .select('username, full_name')
        .eq('id', offer.influencer_id)
        .single();

      if (offer) {
        setOfferData({
          id: offer.id,
          title: offer.title,
          budget: offer.budget,
          deadline: offer.deadline,
          status: offer.status || 'pending',
          business_name: offer.businesses?.company_name || 'Nepoznato',
          influencer_name:
            influencerProfile?.full_name ||
            influencerProfile?.username ||
            'Nepoznato',
        });
      }
    } catch (error) {
      console.error('Error loading offer data:', error);
    }
  };

  const getContextLink = () => {
    if (!user) return '#';

    const userType = user.user_metadata?.user_type || 'influencer';

    if (campaignApplicationId) {
      if (userType === 'business') {
        return `/dashboard/biznis/applications/${campaignApplicationId}`;
      } else {
        return `/dashboard/influencer/applications/${campaignApplicationId}`;
      }
    } else if (offerId) {
      if (userType === 'business') {
        return `/dashboard/biznis/offers/${offerId}`;
      } else {
        return `/dashboard/influencer/offers/${offerId}`;
      }
    }

    return '#';
  };

  const getContextTitle = () => {
    if (campaignData) return campaignData.title;
    if (offerData) return offerData.title;
    if (campaignApplicationId) return 'Kampanja';
    if (offerId) return 'Direktna ponuda';
    return 'Razgovor';
  };

  const getContextType = () => {
    if (campaignApplicationId) return 'Kampanja';
    if (offerId) return 'Direktna ponuda';
    return 'Razgovor';
  };

  const getBudget = () => {
    if (campaignData) return campaignData.budget;
    if (offerData) return offerData.budget;
    return null;
  };

  const getDeadline = () => {
    if (campaignData) return campaignData.end_date;
    if (offerData) return offerData.deadline;
    return null;
  };

  const getStatus = () => {
    if (campaignData) return campaignData.status;
    if (offerData) return offerData.status;
    return null;
  };

  const getStatusBadge = (status: string | null) => {
    if (!status) return null;

    switch (status) {
      case 'active':
      case 'accepted':
        return (
          <Badge variant="default" className="bg-green-500">
            {status === 'active' ? 'Aktivna' : 'Prihvaćeno'}
          </Badge>
        );
      case 'pending':
        return <Badge variant="secondary">Na čekanju</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Odbačeno</Badge>;
      case 'completed':
        return (
          <Badge variant="outline" className="border-green-500 text-green-700">
            Završeno
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="border-b bg-gray-50">
        <div className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-900"></div>
              <span className="text-sm text-muted-foreground">
                Učitava kontekst...
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="border-l-4 border-l-primary bg-gradient-to-r from-blue-50/50 to-purple-50/50 border-b">
      <div className="p-3">
        <div className="flex flex-col gap-2">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3 flex-1">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center">
                {campaignApplicationId ? (
                  <Target className="h-4 w-4 text-white" />
                ) : (
                  <Building2 className="h-4 w-4 text-white" />
                )}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-gray-900 truncate text-sm">
                    {getContextTitle()}
                  </h3>
                  {getStatusBadge(getStatus())}
                </div>

                <div className="flex items-center gap-1 text-xs text-muted-foreground mb-2">
                  <span className="font-medium">{getContextType()}</span>
                  {campaignData?.company_name && (
                    <>
                      <span>•</span>
                      <span className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {campaignData.company_name}
                      </span>
                    </>
                  )}
                  {offerData?.business_name && (
                    <>
                      <span>•</span>
                      <span className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {offerData.business_name}
                      </span>
                    </>
                  )}
                </div>

                {getDeadline() && (
                  <div className="flex items-center gap-1 text-xs">
                    <span className="flex items-center gap-1 bg-orange-50 text-orange-700 px-2 py-1 rounded-full">
                      <Calendar className="h-3 w-3" />
                      <span className="font-medium">{formatDate(getDeadline()!)}</span>
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            {getBudget() && (
              <div className="flex-shrink-0">
                <span className="text-lg font-bold text-green-600">
                  {getBudget()?.toLocaleString()} €
                </span>
              </div>
            )}

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={getContextLink()}>
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Detalji
                </Link>
              </Button>

              {campaignApplicationId && campaignData?.id && (
                <Button variant="default" size="sm" asChild>
                  <Link href={`/campaigns/${campaignData.id}`}>
                    <Target className="h-3 w-3 mr-1" />
                    Kampanja
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
