import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Calendar, Euro, Eye, Edit, Play, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { Campaign } from '@/lib/types';

interface Platform {
  name: string;
  icon: string;
  content_types: string[];
}

interface CampaignWithPlatforms extends Campaign {
  platforms?: Platform[];
}

interface CampaignCardProps {
  campaign: CampaignWithPlatforms;
  onActivate?: (id: string) => void;
  isActivating?: boolean;
}

const CampaignCard: React.FC<CampaignCardProps> = ({ 
  campaign,
  onActivate,
  isActivating = false
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'draft':
        return 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-200';
      case 'paused':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'completed':
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
      case 'cancelled':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      default:
        return 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-200';
    }
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      draft: 'Neaktivna',
      active: 'Aktivna',
      paused: 'Pauzirana',
      completed: 'Završena',
      cancelled: 'Otkazana',
    };
    return labels[status] || status;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sr-RS', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getContentTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'post': 'Photo Feed Post',
      'story': 'Story',
      'reel': 'Reel',
      'video': 'Video',
      'blog': 'Blog Post'
    };
    return labels[type] || type;
  };

  return (
    <Card className="w-full bg-gradient-to-br from-purple-50/50 via-pink-50/30 to-violet-50/50 border-purple-200/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <CardTitle className="text-xl font-semibold text-foreground bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            {campaign.title}
          </CardTitle>
          <Badge 
            variant="outline" 
            className={`${getStatusColor(campaign.status)} font-medium`}
          >
            {getStatusLabel(campaign.status)}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <p className="text-muted-foreground text-sm leading-relaxed">
          {campaign.description}
        </p>
        
        <Separator className="bg-gradient-to-r from-purple-200 to-pink-200" />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center gap-2 p-3 rounded-lg bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-100">
            <Euro className="w-4 h-4 text-purple-600" />
            <span className="text-sm font-medium text-foreground">
              Budžet: <span className="text-purple-700 font-semibold">{campaign.budget?.toLocaleString() || 'N/A'} €</span>
            </span>
          </div>
          
          <div className="flex items-center gap-2 p-3 rounded-lg bg-gradient-to-r from-pink-50 to-violet-50 border border-pink-100">
            <Calendar className="w-4 h-4 text-pink-600" />
            <span className="text-sm font-medium text-foreground">
              Datum kreiranja: <span className="text-pink-700 font-semibold">{formatDate(campaign.created_at)}</span>
            </span>
          </div>
        </div>
        
        {campaign.platforms && campaign.platforms.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-foreground">Platforme i Content Type:</h4>
            <div className="space-y-2">
              {campaign.platforms.map((platform, index) => (
                <div key={index} className="p-3 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 border border-violet-100">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg">{platform.icon}</span>
                    <span className="font-medium text-violet-700">{platform.name}</span>
                  </div>
                  <div className="flex flex-wrap gap-1 ml-6">
                    {platform.content_types?.map((type, typeIndex) => (
                      <Badge 
                        key={typeIndex} 
                        variant="secondary" 
                        className="text-xs bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                      >
                        {getContentTypeLabel(type)}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Fallback za content_types ako nema platforme */}
        {(!campaign.platforms || campaign.platforms.length === 0) && campaign.content_types && campaign.content_types.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-foreground">Tipovi sadržaja:</h4>
            <div className="flex flex-wrap gap-1">
              {campaign.content_types.map((type, index) => (
                <Badge 
                  key={index} 
                  variant="secondary" 
                  className="text-xs bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                >
                  {getContentTypeLabel(type)}
                </Badge>
              ))}
            </div>
          </div>
        )}
        
        <Separator className="bg-gradient-to-r from-purple-200 to-pink-200" />
        
        <div className="flex flex-wrap gap-2 pt-2">
          <Link href={`/campaigns/${campaign.id}`}>
            <Button 
              variant="outline" 
              size="sm" 
              className="flex items-center gap-2 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 text-purple-700 hover:bg-gradient-to-r hover:from-purple-100 hover:to-pink-100 hover:border-purple-300"
            >
              <Eye className="w-4 h-4" />
              Pregled
            </Button>
          </Link>
          
          {campaign.status === 'draft' && (
            <>
              <Link href={`/campaigns/${campaign.id}/edit`}>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center gap-2 bg-gradient-to-r from-pink-50 to-violet-50 border-pink-200 text-pink-700 hover:bg-gradient-to-r hover:from-pink-100 hover:to-violet-100 hover:border-pink-300"
                >
                  <Edit className="w-4 h-4" />
                  Uredi
                </Button>
              </Link>
              
              {onActivate && (
                <Button 
                  size="sm" 
                  onClick={() => onActivate(campaign.id)}
                  disabled={isActivating}
                  className="flex items-center gap-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {isActivating ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                  Aktiviraj
                </Button>
              )}
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CampaignCard;
