'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  searchCampaigns,
  getFeaturedCampaigns,
  CampaignFilters,
} from '@/lib/campaigns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Loader2,
  MapPin,
  Calendar,
  DollarSign,
  Users,
  Eye,
  MessageCircle,
  Star,
  TrendingUp,
} from 'lucide-react';
import { CampaignFiltersComponent } from '@/components/campaigns/campaign-filters';
import { formatDistanceToNow } from 'date-fns';
import { bs } from 'date-fns/locale';
import { formatDate } from '@/lib/date-utils';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import MarketplaceCampaignCard from '@/components/campaigns/MarketplaceCampaignCard';
import { supabase } from '@/lib/supabase';

interface Platform {
  name: string;
  icon: string;
  content_types: string[];
}

interface Campaign {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: string;
  location: string | null;
  application_deadline: string | null;
  min_followers: number | null;
  max_followers: number | null;
  age_range_min: number | null;
  age_range_max: number | null;
  gender: string | null;
  is_featured: boolean;
  created_at: string;
  business_id: string;
  content_types: string[] | null;
  platforms?: Platform[];
  // Optional joined data
  businesses?: {
    company_name: string;
    industry: string;
  };
}

export default function CampaignsMarketplacePage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [featuredCampaigns, setFeaturedCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<CampaignFilters>({});
  const [sortBy, setSortBy] = useState<
    'created_at' | 'budget' | 'application_deadline' | 'applications_count'
  >('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }
  }, [user, authLoading, router]);

  // Load campaigns
  useEffect(() => {
    if (user) {
      loadCampaigns();
      loadFeaturedCampaigns();
    }
  }, [user, searchQuery, filters, sortBy, sortOrder]);

  const loadCampaigns = async () => {
    try {
      setLoading(true);
      const { data, error } = await searchCampaigns({
        search: searchQuery || undefined,
        ...filters,
        sortBy,
        sortOrder,
        limit: 20,
      });

      if (error) {
        console.error('Error loading campaigns:', error);
        return;
      }

      // Load platforms for each campaign
      const campaignsWithPlatforms = await Promise.all(
        (data || []).map(async (campaign) => {
          const { data: platformsData } = await supabase
            .from('campaign_platforms')
            .select(`
              content_types,
              platforms!inner(
                id,
                name,
                slug,
                icon
              )
            `)
            .eq('campaign_id', campaign.id);

          const platforms = platformsData?.map(cp => ({
            name: cp.platforms.name,
            icon: cp.platforms.icon,
            content_types: cp.content_types
          })) || [];

          return { ...campaign, platforms };
        })
      );

      setCampaigns(campaignsWithPlatforms);
    } catch (error) {
      console.error('Error loading campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadFeaturedCampaigns = async () => {
    try {
      const { data, error } = await getFeaturedCampaigns(6);

      if (data) {
        // Load platforms for featured campaigns too
        const featuredWithPlatforms = await Promise.all(
          data.map(async (campaign) => {
            const { data: platformsData } = await supabase
              .from('campaign_platforms')
              .select(`
                content_types,
                platforms!inner(
                  id,
                  name,
                  slug,
                  icon
                )
              `)
              .eq('campaign_id', campaign.id);

            const platforms = platformsData?.map(cp => ({
              name: cp.platforms.name,
              icon: cp.platforms.icon,
              content_types: cp.content_types
            })) || [];

            return { ...campaign, platforms };
          })
        );

        setFeaturedCampaigns(featuredWithPlatforms);
      }
    } catch (error) {
      console.error('Error loading featured campaigns:', error);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadCampaigns();
  };

  const handleFiltersChange = (newFilters: CampaignFilters) => {
    setFilters(newFilters);
  };

  const toggleSort = () => {
    setSortOrder(prev => (prev === 'asc' ? 'desc' : 'asc'));
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: { label: 'Aktivna', variant: 'default' as const },
      paused: { label: 'Pauzirana', variant: 'outline' as const },
    };

    const statusInfo = statusMap[status as keyof typeof statusMap];
    return statusInfo ? (
      <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>
    ) : null;
  };

  const getCollaborationTypeLabel = (type: string) => {
    const typeMap = {
      paid: 'Plaćena',
      barter: 'Barter',
      hybrid: 'Hibridna',
    };
    return typeMap[type as keyof typeof typeMap] || type;
  };

  if (authLoading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-foreground">
            Kampanje
          </h1>
          <p className="text-muted-foreground mt-1">
            Pronađite savršene kampanje za vašu publiku
          </p>
        </div>

        {/* Search and Sort */}
        <div className="flex flex-col sm:flex-row gap-4">
          <form
            onSubmit={handleSearch}
            className="flex items-center gap-2 flex-1"
          >
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Pretraži kampanje..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit" variant="outline" size="sm">
              <Search className="h-4 w-4" />
            </Button>
          </form>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filteri
            </Button>

            <Button variant="outline" size="sm" onClick={toggleSort}>
              {sortOrder === 'asc' ? (
                <SortAsc className="h-4 w-4" />
              ) : (
                <SortDesc className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        <div className="flex gap-8">
          {/* Filters Sidebar */}
          {showFilters && (
            <div className="w-80 flex-shrink-0">
              <CampaignFiltersComponent
                filters={filters}
                onFiltersChange={handleFiltersChange}
              />
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1 space-y-8">
            {/* Featured Campaigns */}
            {featuredCampaigns.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <h2 className="text-xl font-semibold">Premium kampanje</h2>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {featuredCampaigns.map(campaign => (
                    <MarketplaceCampaignCard
                      key={campaign.id}
                      campaign={campaign}
                      isPremium={true}
                    />
                  ))}
                </div>
                <Separator className="my-8" />
              </div>
            )}

            {/* All Campaigns */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">Sve kampanje</h2>
                <div className="text-sm text-muted-foreground">
                  {campaigns.length} kampanja pronađeno
                </div>
              </div>

              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : campaigns.length === 0 ? (
                <Card>
                  <CardContent className="py-12 text-center">
                    <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Nema kampanja</h3>
                    <p className="text-muted-foreground">
                      Trenutno nema kampanja koje odgovaraju vašim kriterijima.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {campaigns.map(campaign => (
                    <MarketplaceCampaignCard key={campaign.id} campaign={campaign} />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}


