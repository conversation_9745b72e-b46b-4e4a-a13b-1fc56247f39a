'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getCampaignWithDetails, hasInfluencerApplied, updateCampaignStatus } from '@/lib/campaigns';
import { CampaignDetails, InfluencerApplicationResponse } from '@/lib/types';
import { getOrCreateProfile } from '@/lib/profiles';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { GradientButton } from '@/components/ui/gradient-button';
import { BackButton } from '@/components/ui/back-button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  ArrowLeft,
  Loader2,
  MapPin,
  Calendar,
  Euro,
  Users,
  Tag,
  MessageCircle,
  Clock,
  Target,
  CheckCircle,
  AlertCircle,
  Send,
  Edit,
  ChevronDown,
  ChevronUp,
  Hash,
  Ban,
  FileText,
  Package,
  Handshake,
  CreditCard,
  Shield,
  RotateCcw,
  Users2,
  Filter,
  Mail,
  Phone,
} from 'lucide-react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { bs } from 'date-fns/locale';
import { CampaignApplicationForm } from '@/components/campaigns/campaign-application-form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';


export default function CampaignDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [campaign, setCampaign] = useState<CampaignDetails | null>(null);
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [isActivating, setIsActivating] = useState(false);
  const [applicationStatus, setApplicationStatus] =
    useState<InfluencerApplicationResponse>({ hasApplied: false });

  const campaignId = params.id as string;

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }

    if (user && campaignId) {
      loadData();
    }
  }, [user, authLoading, campaignId, router]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load profile first
      const { data: profileData, error: profileError } = await getOrCreateProfile(user!.id);
      if (profileError || !profileData) {
        setError('Greška pri učitavanju profila');
        return;
      }
      setProfile(profileData);

      // Load campaign details
      const { data, error } = await getCampaignWithDetails(campaignId);

      if (error) {
        setError('Greška pri učitavanju kampanje');
        return;
      }

      if (!data) {
        setError('Kampanja nije pronađena');
        return;
      }

      setCampaign(data);

      // Check if current user is the owner of the campaign
      setIsOwner(data?.business_id === user?.id);

      // Check if influencer already applied (only for non-owners)
      if (data?.business_id !== user?.id && user?.id) {
        const { data: applicationData } = await hasInfluencerApplied(
          campaignId,
          user.id
        );
        if (applicationData) {
          setApplicationStatus(applicationData);
        }
      }
    } catch (err) {
      setError('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !campaign) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <CardTitle>Greška</CardTitle>
            <CardDescription>
              {error || 'Kampanja nije pronađena'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.back()}
            >
              Nazad
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      draft: { label: 'Nacrt', variant: 'secondary' as const },
      active: { label: 'Aktivna', variant: 'default' as const },
      paused: { label: 'Pauzirana', variant: 'outline' as const },
      completed: { label: 'Završena', variant: 'secondary' as const },
      cancelled: { label: 'Otkazana', variant: 'destructive' as const },
    };

    const statusInfo =
      statusMap[status as keyof typeof statusMap] || statusMap.draft;
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  const getCollaborationTypeLabel = (type: string) => {
    const typeMap = {
      paid: 'Plaćena saradnja',
      barter: 'Barter (razmena)',
      hybrid: 'Hibridna saradnja',
    };
    return typeMap[type as keyof typeof typeMap] || type;
  };

  const getPaymentTermsLabel = (terms: string) => {
    const termsMap = {
      upfront: 'Unapred',
      '50_50': '50% unapred, 50% po završetku',
      on_delivery: 'Po isporuci',
    };
    return termsMap[terms as keyof typeof termsMap] || terms;
  };

  const getUsageRightsLabel = (rights: string) => {
    const rightsMap = {
      single_use: 'Jednokratna upotreba',
      unlimited: 'Neograničena upotreba',
      '6_months': '6 meseci',
      '1_year': '1 godina',
    };
    return rightsMap[rights as keyof typeof rightsMap] || rights;
  };

  const getGenderLabel = (gender: string) => {
    const genderMap = {
      male: 'Muški',
      female: 'Ženski',
      other: 'Ostalo',
      prefer_not_to_say: 'Ne želim da kažem',
    };
    return genderMap[gender as keyof typeof genderMap] || 'Svi';
  };

  const handleActivateCampaign = async () => {
    if (!campaign) return;

    setIsActivating(true);
    try {
      const { data, error } = await updateCampaignStatus(campaign.id, 'active');

      if (error) {
        toast.error('Greška pri aktiviranju kampanje: ' + error.message);
        return;
      }

      // Update local state
      setCampaign(prev => prev ? { ...prev, status: 'active' } : null);
      toast.success('Kampanja je uspješno aktivirana!');
    } catch (error) {
      console.error('Error activating campaign:', error);
      toast.error('Greška pri aktiviranju kampanje');
    } finally {
      setIsActivating(false);
    }
  };

  // Use actual profile data for the header
  const headerProfile = profile || {
    avatar_url: user?.user_metadata?.avatar_url,
    full_name: user?.user_metadata?.full_name,
    username: user?.user_metadata?.username || user?.email?.split('@')[0],
  };

  return (
    <DashboardLayout requiredUserType={profile?.user_type || 'influencer'}>
      {/* Main Container with gradient background */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
        {/* Dreamy gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />

        {/* Subtle glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 dark:from-purple-600/10 dark:via-pink-600/5 dark:to-purple-500/10 blur-xl opacity-50" />

        <div className="relative p-6 space-y-6">
          {/* Header */}
          <div className="flex items-start gap-4">
            {/* Back button */}
            <BackButton onClick={() => router.back()} />

            <div className="flex-1 min-w-0">
              <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                {campaign.title}
              </h1>
              <div className="flex flex-wrap items-center gap-4 text-sm mt-2">
                <div className="flex items-center gap-2">
                  <MessageCircle className="h-4 w-4 text-purple-500" />
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {campaign.applications_count || 0}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {(campaign.applications_count || 0) === 1 ? 'aplikacija' : 'aplikacija'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-pink-500" />
                  <span className="text-gray-600 dark:text-gray-400">
                    Objavljena {campaign.created_at &&
                      formatDistanceToNow(new Date(campaign.created_at), {
                        addSuffix: true,
                        locale: bs,
                      })}
                  </span>
                </div>
              </div>
            </div>

            {/* Status badge */}
            {campaign.status && (
              <Badge
                variant="outline"
                className={`${
                  campaign.status === 'active'
                    ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200'
                    : 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200'
                } font-medium`}
              >
                {campaign.status === 'active' ? 'Aktivna' : 'Neaktivna'}
              </Badge>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Description */}
              <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-6 border border-purple-100/50 dark:border-purple-800/30">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2 mb-4">
                  <FileText className="h-5 w-5 text-purple-500" />
                  Opis kampanje
                </h3>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                  {campaign.description}
                </p>
              </div>

              {/* Platforms and Content Types */}
              {campaign.platforms && campaign.platforms.length > 0 && (
                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-6 border border-purple-100/50 dark:border-purple-800/30">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                    Platforme i tipovi sadržaja
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    Gde influencer treba da objavi sadržaj i koji tip sadržaja
                  </p>
                  <div className="space-y-4">
                    {campaign.platforms.map((platform, index) => (
                      <div key={index} className="bg-white/80 dark:bg-gray-700/40 rounded-lg p-4 border border-purple-100/30 dark:border-purple-800/20">
                        <div className="flex items-center gap-2 mb-3">
                          <span className="text-lg">{platform.icon}</span>
                          <span className="font-medium text-gray-800 dark:text-gray-200">{platform.name}</span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {platform.content_types?.map((type, typeIndex) => (
                            <Badge
                              key={typeIndex}
                              variant="secondary"
                              className="bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                            >
                              {type === 'post' ? 'Photo post' :
                               type === 'video' ? 'Video' :
                               type === 'story' ? 'Story' :
                               type === 'reel' ? 'Shorts' : type}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}



              {/* Hashtags */}
              {campaign.hashtags && campaign.hashtags.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Hash className="h-5 w-5" />
                      Obavezni hashtag-ovi
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {campaign.hashtags.map((hashtag, index) => (
                        <Badge key={index} variant="outline" className="font-mono">
                          #{hashtag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Do Not Mention */}
              {campaign.do_not_mention && campaign.do_not_mention.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Ban className="h-5 w-5" />
                      Ne spominjati
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {campaign.do_not_mention.map((item, index) => (
                        <Badge key={index} variant="destructive">
                          {item}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Target Audience */}
              {(campaign.age_range_min || campaign.age_range_max || campaign.gender) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users2 className="h-5 w-5" />
                      Traženi profil influencera
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Age Range */}
                    {(campaign.age_range_min || campaign.age_range_max) && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Uzrast</span>
                        <span className="font-medium">
                          {campaign.age_range_min || 13} - {campaign.age_range_max || 100} godina
                        </span>
                      </div>
                    )}

                    {/* Gender */}
                    {campaign.gender && campaign.gender !== 'all' && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Pol</span>
                        <span className="font-medium">
                          {getGenderLabel(campaign.gender)}
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Content Categories */}
              {campaign.categories && campaign.categories.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Tag className="h-5 w-5" />
                      Kategorije sadržaja koji influencer inače kreira
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {campaign.categories.map((category: any) => (
                        <Badge key={category.id} variant="secondary" className="flex items-center gap-1">
                          {category.icon && (
                            <span className="text-sm">{category.icon}</span>
                          )}
                          {category.name}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Collaboration Type */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Handshake className="h-5 w-5" />
                    Tip saradnje
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="font-medium">
                    {getCollaborationTypeLabel(campaign.collaboration_type || 'paid')}
                  </p>
                </CardContent>
              </Card>



              {/* Additional Notes */}
              {campaign.additional_notes && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Dodatne napomene
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                      {campaign.additional_notes}
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>


          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Business Info */}
            <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-6 border border-purple-100/50 dark:border-purple-800/30">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Biznis</h3>
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={campaign.business_avatar || undefined} />
                  <AvatarFallback className="bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700">
                    {campaign.company_name?.charAt(0).toUpperCase() || 'B'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">{campaign.company_name}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    @{campaign.business_username}
                  </p>
                </div>
              </div>
            </div>

            {/* Campaign Details */}
            <Card>
              <CardHeader>
                <CardTitle>Detalji kampanje</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Budžet</span>
                  <span className="font-medium">
                    {campaign.budget?.toLocaleString() || 'Nije specificirano'} €
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Tip saradnje
                  </span>
                  <span className="font-medium">
                    {getCollaborationTypeLabel(campaign.collaboration_type || 'paid')}
                  </span>
                </div>






              </CardContent>
            </Card>

            {/* Action Buttons - Different for Owner vs Influencer */}
            <div className="space-y-3">
              {isOwner ? (
                // Owner (Business) View
                <>
                  {campaign.status === 'draft' ? (
                    // Draft campaign - show Activate button
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={handleActivateCampaign}
                      disabled={isActivating}
                    >
                      {isActivating ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <CheckCircle className="mr-2 h-5 w-5" />
                      )}
                      {isActivating ? 'Aktiviranje...' : 'Aktiviraj kampanju'}
                    </Button>
                  ) : (
                    // Active campaign - show Applications button
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={() =>
                        router.push(
                          `/dashboard/biznis/applications?campaign=${campaign.id}`
                        )
                      }
                    >
                      <Users className="mr-2 h-5 w-5" />
                      Pregled aplikacija ({campaign.applications_count})
                    </Button>
                  )}

                  {/* Edit button only for draft campaigns */}
                  {campaign.status === 'draft' && (
                    <Button
                      variant="outline"
                      className="w-full"
                      size="lg"
                      onClick={() =>
                        router.push(`/campaigns/${campaign.id}/edit`)
                      }
                    >
                      <Edit className="mr-2 h-5 w-5" />
                      Uredi kampanju
                    </Button>
                  )}
                </>
              ) : (
                // Influencer View
                <>
                  {applicationStatus.hasApplied ? (
                    // Show application status
                    <Card className="w-full">
                      <CardContent className="pt-6">
                        <div className="flex items-center gap-3 mb-4">
                          <CheckCircle className="h-6 w-6 text-green-500" />
                          <div>
                            <h3 className="font-semibold">
                              Aplikacija poslana
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              Prijavili ste se na ovu kampanju{' '}
                              {applicationStatus.appliedAt &&
                                formatDistanceToNow(
                                  new Date(applicationStatus.appliedAt),
                                  { addSuffix: true, locale: bs }
                                )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              applicationStatus.application?.status === 'accepted'
                                ? 'default'
                                : applicationStatus.application?.status === 'rejected'
                                  ? 'destructive'
                                  : 'secondary'
                            }
                          >
                            {applicationStatus.application?.status === 'pending' &&
                              'Čeka se odgovor'}
                            {applicationStatus.application?.status === 'accepted' &&
                              'Prihvaćeno'}
                            {applicationStatus.application?.status === 'rejected' &&
                              'Odbačeno'}
                          </Badge>
                        </div>
                        {applicationStatus.application?.status === 'pending' && (
                          <p className="text-sm text-muted-foreground mt-2">
                            Biznis će uskoro pregledati vašu aplikaciju i
                            odgovoriti.
                          </p>
                        )}
                        {applicationStatus.application?.id && (
                          <div className="mt-4">
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full"
                              onClick={() =>
                                router.push(
                                  `/dashboard/influencer/applications/${applicationStatus.application?.id}`
                                )
                              }
                            >
                              Pogledajte detalje vaše aplikacije
                            </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ) : (
                    // Show application form
                    <Dialog
                      open={showApplicationForm}
                      onOpenChange={setShowApplicationForm}
                    >
                      <DialogTrigger asChild>
                        <GradientButton gradientVariant="primary" className="w-full" size="lg">
                          <Send className="mr-2 h-5 w-5" />
                          Apliciraj
                        </GradientButton>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Aplikacija za kampanju</DialogTitle>
                        </DialogHeader>
                        <CampaignApplicationForm
                          campaign={{
                            id: campaign.id,
                            title: campaign.title,
                            budget: campaign.budget,
                            description: campaign.description,
                            company_name: campaign.company_name,
                            platforms: campaign.platforms,
                          }}
                          onSuccess={() => {
                            setShowApplicationForm(false);
                            // Reload to show application status
                            loadCampaign();
                          }}
                          onCancel={() => setShowApplicationForm(false)}
                        />
                      </DialogContent>
                    </Dialog>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
