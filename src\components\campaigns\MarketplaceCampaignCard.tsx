import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Calendar, Euro, MapPin, Users, Star, Crown } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { formatDate } from '@/lib/date-utils';
import { formatDistanceToNow } from 'date-fns';
import { bs } from 'date-fns/locale';

interface Platform {
  name: string;
  icon: string;
  content_types: string[];
}

interface Campaign {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: string;
  location: string | null;
  application_deadline: string | null;
  min_followers: number | null;
  max_followers: number | null;
  age_range_min: number | null;
  age_range_max: number | null;
  gender: string | null;
  is_featured: boolean;
  created_at: string;
  business_id: string;
  content_types: string[] | null;
  platforms?: Platform[];
  businesses?: {
    company_name: string;
    industry: string;
  };
}

interface MarketplaceCampaignCardProps {
  campaign: Campaign;
  isPremium?: boolean;
}

const MarketplaceCampaignCard: React.FC<MarketplaceCampaignCardProps> = ({ 
  campaign,
  isPremium = false
}) => {
  const router = useRouter();

  const handleClick = () => {
    router.push(`/campaigns/${campaign.id}`);
  };

  const getContentTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'post': 'Photo Feed Post',
      'story': 'Story',
      'reel': 'Reel',
      'video': 'Video',
      'blog': 'Blog Post'
    };
    return labels[type] || type;
  };

  const getFollowersRange = () => {
    if (campaign.min_followers && campaign.max_followers) {
      return `${campaign.min_followers.toLocaleString()} - ${campaign.max_followers.toLocaleString()}`;
    } else if (campaign.min_followers) {
      return `${campaign.min_followers.toLocaleString()}+`;
    } else if (campaign.max_followers) {
      return `do ${campaign.max_followers.toLocaleString()}`;
    }
    return null;
  };

  return (
    <div 
      className={`relative overflow-hidden rounded-2xl cursor-pointer transition-all duration-300 group hover:scale-[1.02] ${
        isPremium 
          ? 'bg-gradient-to-br from-amber-50/80 via-yellow-50/60 to-orange-100/80 dark:from-amber-950/20 dark:via-yellow-950/10 dark:to-orange-900/30 border border-amber-200/50 dark:border-amber-800/30'
          : 'bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30'
      } backdrop-blur-sm shadow-lg hover:shadow-xl`}
      onClick={handleClick}
    >
      {/* Dreamy gradient overlay */}
      <div className={`absolute inset-0 opacity-60 group-hover:opacity-80 transition-opacity duration-300 ${
        isPremium
          ? 'bg-gradient-to-br from-amber-100/30 via-yellow-100/20 to-orange-200/40 dark:from-amber-900/10 dark:via-yellow-900/5 dark:to-orange-800/20'
          : 'bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20'
      }`} />
      
      {/* Subtle glow effect */}
      <div className={`absolute inset-0 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300 ${
        isPremium
          ? 'bg-gradient-to-r from-amber-300/20 via-yellow-300/10 to-orange-400/20 dark:from-amber-600/10 dark:via-yellow-600/5 dark:to-orange-500/10'
          : 'bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 dark:from-purple-600/10 dark:via-pink-600/5 dark:to-purple-500/10'
      }`} />
      
      <div className="relative p-6 space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              {isPremium && (
                <div className="flex items-center gap-1">
                  <Crown className="w-4 h-4 text-amber-600" />
                  <Badge className="bg-gradient-to-r from-amber-400 to-yellow-500 text-amber-900 border-amber-300 font-semibold text-xs">
                    Premium
                  </Badge>
                </div>
              )}
            </div>
            <h3 className={`text-xl font-semibold line-clamp-2 ${
              isPremium 
                ? 'bg-gradient-to-r from-amber-700 to-orange-600 bg-clip-text text-transparent'
                : 'bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent'
            }`}>
              {campaign.title}
            </h3>
            {campaign.businesses && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {campaign.businesses.company_name}
              </p>
            )}
          </div>
          <div className="flex flex-col items-end gap-1 flex-shrink-0">
            <div className={`text-lg font-bold whitespace-nowrap ${
              isPremium ? 'text-amber-700' : 'text-purple-700'
            }`}>
              {campaign.budget.toLocaleString()} €
            </div>
          </div>
        </div>
        
        {/* Description */}
        <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed line-clamp-2">
          {campaign.description}
        </p>
        
        {/* Requirements */}
        <div className="grid grid-cols-1 gap-2">
          {getFollowersRange() && (
            <div className="flex items-center gap-2 text-sm">
              <Users className={`w-4 h-4 ${isPremium ? 'text-amber-600' : 'text-purple-500'}`} />
              <span className="text-gray-600 dark:text-gray-400">Pratilaca:</span>
              <span className="font-semibold text-gray-900 dark:text-gray-100">{getFollowersRange()}</span>
            </div>
          )}
          
          {campaign.location && (
            <div className="flex items-center gap-2 text-sm">
              <MapPin className={`w-4 h-4 ${isPremium ? 'text-amber-600' : 'text-pink-500'}`} />
              <span className="text-gray-600 dark:text-gray-400">Lokacija:</span>
              <span className="font-semibold text-gray-900 dark:text-gray-100">{campaign.location}</span>
            </div>
          )}
          
          {campaign.application_deadline && (
            <div className="flex items-center gap-2 text-sm">
              <Calendar className={`w-4 h-4 ${isPremium ? 'text-amber-600' : 'text-pink-500'}`} />
              <span className="text-gray-600 dark:text-gray-400">Rok:</span>
              <span className="font-semibold text-gray-900 dark:text-gray-100">{formatDate(campaign.application_deadline)}</span>
            </div>
          )}
        </div>
        
        {/* Platforms */}
        {campaign.platforms && campaign.platforms.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">Platforme i Content Type:</h4>
            <div className="space-y-2">
              {campaign.platforms.slice(0, 2).map((platform, index) => (
                <div key={index} className={`rounded-lg p-3 border ${
                  isPremium 
                    ? 'bg-white/60 dark:bg-gray-800/40 border-amber-100/50 dark:border-amber-800/30'
                    : 'bg-white/60 dark:bg-gray-800/40 border-purple-100/50 dark:border-purple-800/30'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg">{platform.icon}</span>
                    <span className="font-medium text-gray-800 dark:text-gray-200 text-sm">{platform.name}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {platform.content_types?.slice(0, 3).map((type, typeIndex) => (
                      <Badge 
                        key={typeIndex} 
                        variant="secondary" 
                        className={`text-xs ${
                          isPremium 
                            ? 'bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-700 border-amber-200'
                            : 'bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200'
                        }`}
                      >
                        {getContentTypeLabel(type)}
                      </Badge>
                    ))}
                    {platform.content_types && platform.content_types.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{platform.content_types.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
              {campaign.platforms.length > 2 && (
                <div className="text-xs text-gray-500 text-center">
                  +{campaign.platforms.length - 2} više platformi
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Fallback za content_types ako nema platforme */}
        {(!campaign.platforms || campaign.platforms.length === 0) && campaign.content_types && campaign.content_types.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">Tipovi sadržaja:</h4>
            <div className="flex flex-wrap gap-1">
              {campaign.content_types.slice(0, 4).map((type, index) => (
                <Badge 
                  key={index} 
                  variant="secondary" 
                  className={`text-xs ${
                    isPremium 
                      ? 'bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-700 border-amber-200'
                      : 'bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200'
                  }`}
                >
                  {getContentTypeLabel(type)}
                </Badge>
              ))}
              {campaign.content_types.length > 4 && (
                <Badge variant="secondary" className="text-xs">
                  +{campaign.content_types.length - 4}
                </Badge>
              )}
            </div>
          </div>
        )}
        
        {/* Time ago */}
        <div className="text-xs text-gray-500 pt-2 border-t border-gray-200/50">
          Objavljeno {formatDistanceToNow(new Date(campaign.created_at), {
            addSuffix: true,
            locale: bs,
          })}
        </div>
      </div>
    </div>
  );
};

export default MarketplaceCampaignCard;
